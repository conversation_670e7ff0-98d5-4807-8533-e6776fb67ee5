import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import veauryVitePlugins from 'veaury/vite/index'
import vue from '@vitejs/plugin-vue';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { VantResolver } from '@vant/auto-import-resolver';

// https://vitejs.dev/config/
export default defineConfig(({command}) => {
  const isDev = command === 'serve'
  // 判断是否为开发环境
  let USE_LOCAL_API:any = {}
  if(isDev){
    /**
     * 是否使用本地API
     * true：使用本地服务
     * false：使用局域网服务
     */
    try {
      // 动态加载配置文件
      const APIConfig = require('./APIConifg.json');
      USE_LOCAL_API = APIConfig;
    } catch (error) {
      console.warn('APIConifg.json not found, using default configuration');
    } 
  }

  return {
    base: isDev ? '/' : '/static/app/',
    //base:'/static/DataPreprocess/',
    //base: command === 'serve' ? '/' : '/static/DataPreprocess/',
    plugins: [
      veauryVitePlugins({
        type: 'vue',
        // @ts-ignore
        configuration: {  
          useVueInReact: true,
          useReactInVue: true
        }
      }),
      AutoImport({
        resolvers: [VantResolver()],
      }),
      Components({
        resolvers: [VantResolver()],
      }),
    ],
    resolve: {
      alias: {
        // @ts-ignore
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    }, 
    server: {
      host: '0.0.0.0', // 允许外部访问
      proxy: {
        // 代理所有以 /api 开头的请求
        '/api/v1': {
          target: 'http://***************:9112', // 后端服务器地址
          changeOrigin: true, // 修改请求头中的Origin为目标地址
          // rewrite: (path) => path.replace(/^\/api/, ''), // 可选：重写路径，移除/api前缀
          // 其他可选配置：
          // secure: false, // 禁用SSL验证（针对https）
          // ws: true, // 代理WebSocket
        },      
        '/api/user': {// UserService // 匹配所有以 '/api/user'开头的请求路径
          target: USE_LOCAL_API.user ? 'http://localhost:50001' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.user ? { '^/api/user': '' } : {},
          changeOrigin: true,
        },
        '/api/resource': {// ResourceService
          target: USE_LOCAL_API.resource ? 'http://localhost:50002' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.resource ? { '^/api/resource': '' } : {},
          changeOrigin: true,
        },
        '/api/oil': {// OilService
          target: USE_LOCAL_API.oil ? 'http://localhost:50003' : 'http://***************:9527',
          followRedirects: true,
          rewrite: (path) => USE_LOCAL_API.oil ? path.replace(/^\/api\/oil/, '') : path,
          changeOrigin: true,
        },
        '/api/log': {// LogService
          target: USE_LOCAL_API.log ? 'http://localhost:50004' : 'http://***************:9527',
          rewrite: (path) => USE_LOCAL_API.log ? path.replace(/^\/api\/log/, '') : path,
          changeOrigin: true,
        },
        '/api/project': {// ProjectService
          target: USE_LOCAL_API.project ? 'http://localhost:50005' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.project ? { '^/api/project': '' } : {},
          changeOrigin: true,
        },
        '/api/Preprocess': {// Preprocess
          target: USE_LOCAL_API.datapreprocess ? 'http://localhost:51002' : 'http://***************:9527',
          rewrite: (path) => USE_LOCAL_API.datapreprocess ? path.replace(/^\/api\/Preprocess/, '') : path,
          changeOrigin: true,
        },
        '/api/GeoSteering': {// GeoSteering
          target: USE_LOCAL_API.GeoSteering ? 'http://localhost:51004' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.GeoSteering ? { '^/api/GeoSteering': '' } : {},
          changeOrigin: true,
        },
        '/api/Witsml': {// Witsml
          target: USE_LOCAL_API.Witsml ? 'http://localhost:50010' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.Witsml ? { '^/api/Witsml': '' } : {},
          changeOrigin: true,
        },
        '/api/backup': {// BackupService
          target: USE_LOCAL_API.backup ? 'http://localhost:50007' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.backup ? { '^/api/backup': '' } : {},
          changeOrigin: true,
        },
        '/api/dwpr': {// DWPRService
          target: USE_LOCAL_API.Witsml ? 'http://localhost:50006' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.Witsml ? { '^/api/dwpr': '' } : {},
          changeOrigin: true,
        },
        '/api/sdk': {// SDKService
          target: USE_LOCAL_API.demo ? 'http://localhost:50008' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.demo ? { '^/api/sdk': '' } : {},
          changeOrigin: true,
        },
        '/api/processModule': {// ProcessModuleService
          target: USE_LOCAL_API.demo ? 'http://localhost:50013' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.demo ? { '^/api/processModule': '' } : {},
          changeOrigin: true,
        },
        '/api/multiWellCompare': {// MultiWellCompare
          target: USE_LOCAL_API.multiWellCompare ? 'http://localhost:51001' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.multiWellCompare ? { '^/api/multiWellCompare': '' } : {},
          changeOrigin: true,
        },
        '/api/markerLayer': {// MarkerLayerService
          target: USE_LOCAL_API.markerLayer ? 'http://localhost:50009' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.markerLayer ? { '^/api/markerLayer': '' } : {},
          changeOrigin: true,
        },
        '/api/visual3D': {// Visual3D
          target: USE_LOCAL_API.visual3D ? 'http://localhost:51003' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.visual3D ? { '^/api/visual3D': '' } : {},
          changeOrigin: true,
        },
        '/ws': {// StateSyncService (WebSocket)
          target: USE_LOCAL_API.ws ? 'http://localhost:50011' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.ws ? { '^/ws': '' } : {},
          changeOrigin: true,
          ws: true,  // 启用 WebSocket 代理
          // 解决 WebSocket 协议升级头丢失问题
          onProxyReqWs: (proxyReq) => {
            proxyReq.setHeader('Sec-WebSocket-Protocol', 'connectionToken');
          },
        },
        '/api/stateSync': {// StateSyncService
          target: USE_LOCAL_API.ws ? 'http://localhost:50011' : 'http://***************:9527',
          pathRewrite: USE_LOCAL_API.ws ? { '^/api/stateSync': '' } : {},
          changeOrigin: true,
        },
      }
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true, // 关键：启用内联 JS
          math: 'always' // 可选：确保 Less 数学计算正常
        }
      }
    }
  }
})
