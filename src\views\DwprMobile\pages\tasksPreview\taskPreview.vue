<template>
  <div ref="containerRef" class="task-preview-container">
    <div class="nav">
        <van-nav-bar @click-left="handleBack">
        <template #left>
            <van-icon name="arrow-left" color="#fff" :size="24" />
            <span class="left-text">PY4-2-A</span>
        </template>
        <template #right>
          <van-icon name="/src/icon/dwprMobile/taskPre-left-icon.svg" color="#fff" :size="24" />
        </template>
      </van-nav-bar>
    </div>
    <div class="content">
        <!-- 预览内容区域 -->
        <div id="well-log-app1-" class="preview-wrapper">
            
        </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useShowTabbarStore } from '@/stores/useShowTabbar.ts'
import { WebCanvas } from './WebCanvas.js'

console.log(WebCanvas, '8888')

const router = useRouter()
const { setShowTabbar } = useShowTabbarStore()

// 横屏状态管理
const isLandscape = ref(false)
const containerRef = ref<HTMLElement>()
const screenWidth = ref(window.innerWidth)
const screenHeight = ref(window.innerHeight)

// 检测屏幕方向
const checkOrientation = () => {
  // 更新屏幕尺寸
  screenWidth.value = window.innerWidth
  screenHeight.value = window.innerHeight

  const orientation = screen.orientation || (screen as any).mozOrientation || (screen as any).msOrientation

  if (orientation) {
    // 使用 Screen Orientation API
    isLandscape.value = orientation.angle === 90 || orientation.angle === -90 || orientation.angle === 270
  } else {
    // 降级方案：使用窗口尺寸判断
    isLandscape.value = window.innerWidth > window.innerHeight
  }

  // 如果是竖屏，强制旋转
  if (!isLandscape.value) {
    forceRotateToLandscape()
  } else {
    resetRotation()
  }
}

// 强制旋转到横屏
const forceRotateToLandscape = () => {
  if (containerRef.value) {
    const container = containerRef.value
    const isPortrait = window.innerHeight > window.innerWidth

    if (isPortrait) {
      // 计算旋转后的尺寸
      const vh = window.innerHeight
      const vw = window.innerWidth

      // 应用旋转变换
      container.style.transform = 'rotate(90deg)'
      container.style.transformOrigin = 'center center'
      container.style.width = `${vh}px`
      container.style.height = `${vw}px`
      container.style.position = 'fixed'
      container.style.top = `${(vh - vw) / 2}px`
      container.style.left = `${(vw - vh) / 2}px`
      container.style.zIndex = '9999'
    }
  }
}

// 重置旋转
const resetRotation = () => {
  if (containerRef.value) {
    const container = containerRef.value
    container.style.transform = ''
    container.style.transformOrigin = ''
    container.style.width = ''
    container.style.height = ''
    container.style.position = ''
    container.style.top = ''
    container.style.left = ''
    container.style.zIndex = ''
  }
}

// 监听屏幕方向变化
const handleOrientationChange = () => {
  setTimeout(() => {
    checkOrientation()
  }, 100) // 延迟确保方向变化完成
}

// 监听窗口大小变化
const handleResize = () => {
  setTimeout(() => {
    checkOrientation()
  }, 100)
}

// 返回按钮处理
const handleBack = () => {
  router.back()
}
let plot: any = null
const loadPlot = () => {

  plot = new WebCanvas.TWCStGeoSteering({
        appContainer: document.getElementById("well-log-app1-"),
        stratu: {
            measure: {
                // meter|inch
               unit: "meter",
                ratio: 200,
            },
            wellTrace: [],
            
        },
        objTypeInfos: [],
        interpretationSegments: [],
        vplot: {
            measure: {
                // meter|inch
                unit: "meter",
                ratio: 200,
                //startIndex: nowProjectInfo.VLogPlot.StartIndex,
                //endIndex: nowProjectInfo.VLogPlot.EndIndex
                // unit: 'inch',
                // ratio: 1/200, // means 1:200
            },
            tracks: [
                
            ],
            topsets: [
                
            ],
        },
        hplot: {
            measure: {
                unit: "meter",
                ratio: 200,
                //startIndex: nowProjectInfo.HLogPlot.StartIndex,
                //endIndex: nowProjectInfo.HLogPlot.EndIndex
            },
            tracks: [
                
            ],
            topsets: [
                
            ],
        },
        exaggeration: 5,
        // leftArrowIcon: "../images/leftArrowCur.png",
        // rightArrowIcon: "../images/rightArrowCur.png",
        // upArrowIcon: "../images/upArrowCur.png",
        // downArrowIcon: "../images/downArrowCur.png",
    });
}

// 生命周期
onMounted(async () => {
  setShowTabbar(false)

  await nextTick()

  // 初始检测
  checkOrientation()

  // 监听方向变化事件
  window.addEventListener('orientationchange', handleOrientationChange)
  window.addEventListener('resize', handleResize)

  // 监听 Screen Orientation API
  if (screen.orientation) {
    screen.orientation.addEventListener('change', handleOrientationChange)
  }
  setTimeout(() => {
    // loadPlot()
  }, 1000)
})

onUnmounted(() => {
  setShowTabbar(true)
  resetRotation()

  // 移除事件监听
  window.removeEventListener('orientationchange', handleOrientationChange)
  window.removeEventListener('resize', handleResize)

  if (screen.orientation) {
    screen.orientation.removeEventListener('change', handleOrientationChange)
  }
})
</script>

<style scoped lang="less">
.task-preview-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    transition: all 0.3s ease;
}

.nav {
    flex-shrink: 0;
    z-index: 100;

    :deep(.van-nav-bar) {
        background-color: #4472C4;
    }
}
.left-text {
  font-size: 1.125rem;
  font-weight: 500;
  color: #fff;
  margin-left: 1.25rem;
}

.content {
    flex: 1;
    overflow: auto;
    .preview-wrapper {
        width: 100%;
        height: 100%;
        padding-top: env(safe-area-inset-top);
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
        padding-bottom: env(safe-area-inset-bottom);
    }
}

// 移动端横屏适配
@media screen and (orientation: landscape) {
    .task-preview-container {
        .nav {
            :deep(.van-nav-bar__title) {
                max-width: calc(100% - 140px);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .content {
            // padding: 16px 20px;
        }
    }
}

// 小屏幕设备适配
@media screen and (max-width: 768px) {
    .nav {
        :deep(.van-nav-bar) {
            height: 44px;
        }

        :deep(.van-nav-bar__title) {
            font-size: 16px;
        }

        :deep(.van-nav-bar__left) {
            .van-icon {
                font-size: 18px;
            }

            .van-nav-bar__text {
                font-size: 14px;
            }
        }
    }

    .content {
        // padding: 16px;
    }
}
</style>