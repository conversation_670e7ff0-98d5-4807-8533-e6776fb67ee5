<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可拖动行排序 Demo</title>
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <!-- Element UI JS -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <!-- Sortable.js 用于拖拽功能 -->
    <script src="https://unpkg.com/sortablejs@1.15.0/Sortable.min.js"></script>

    <style>
        .demo-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }

        .sortable-ghost {
            opacity: 0.8;
            color: #fff !important;
            background: #42b983 !important;
        }

        .sortable-chosen {
            opacity: 0.8;
        }

        .drag-handle {
            cursor: move;
            color: #999;
        }

        .drag-handle:hover {
            color: #409EFF;
        }

        .el-table .sortable-drag {
            background: #f5f7fa;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="demo-container">
            <h1>可拖动行排序 Demo</h1>
            <p>拖动左侧的拖拽图标来重新排序表格行</p>

            <el-table ref="dragTable" :data="tableData" style="width: 100%" row-key="id" border>

                <el-table-column label="序号" width="80" align="center">
                    <template slot-scope="scope">
                        <span class="drag-handle">{{ scope.$index + 1 }}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="name" label="姓名" width="120">
                </el-table-column>

                <el-table-column prop="age" label="年龄" width="80">
                </el-table-column>

                <el-table-column prop="position" label="职位" width="150">
                </el-table-column>

                <el-table-column prop="department" label="部门">
                </el-table-column>

                <el-table-column prop="salary" label="薪资" width="100">
                    <template slot-scope="scope">
                        ¥{{ scope.row.salary }}
                    </template>
                </el-table-column>
            </el-table>

            <div style="margin-top: 20px;">
                <el-button @click="resetOrder" type="primary">重置顺序</el-button>
                <el-button @click="showCurrentOrder" type="info">显示当前顺序</el-button>
            </div>

            <div v-if="orderInfo" style="margin-top: 20px;">
                <el-alert :title="orderInfo" type="success" :closable="false">
                </el-alert>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    tableData: [
                        {
                            id: 1,
                            name: '张三',
                            age: 28,
                            position: '前端工程师',
                            department: '技术部',
                            salary: 15000
                        },
                        {
                            id: 2,
                            name: '李四',
                            age: 32,
                            position: '后端工程师',
                            department: '技术部',
                            salary: 18000
                        },
                        {
                            id: 3,
                            name: '王五',
                            position: '产品经理',
                            age: 30,
                            department: '产品部',
                            salary: 20000
                        },
                        {
                            id: 4,
                            name: '赵六',
                            age: 26,
                            position: 'UI设计师',
                            department: '设计部',
                            salary: 12000
                        },
                        {
                            id: 5,
                            name: '钱七',
                            age: 35,
                            position: '项目经理',
                            department: '项目部',
                            salary: 22000
                        }
                    ],
                    originalData: [],
                    orderInfo: ''
                }
            },
            mounted() {
                // 保存原始数据
                this.originalData = JSON.parse(JSON.stringify(this.tableData));

                // 初始化拖拽功能
                this.initSortable();
            },
            methods: {
                initSortable() {
                    const tbody = this.$refs.dragTable.$el.querySelector('.el-table__body-wrapper tbody');

                    Sortable.create(tbody, {
                        handle: '.drag-handle',
                        animation: 150,
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        dragClass: 'sortable-drag',
                        onEnd: (evt) => {
                            const { oldIndex, newIndex } = evt;

                            // 更新数据顺序
                            const movedItem = this.tableData.splice(oldIndex, 1)[0];
                            this.tableData.splice(newIndex, 0, movedItem);

                            // 显示拖拽结果
                            this.$message.success(`已将 ${movedItem.name} 从第 ${oldIndex + 1} 行移动到第 ${newIndex + 1} 行`);
                        }
                    });
                },

                resetOrder() {
                    this.tableData = JSON.parse(JSON.stringify(this.originalData));
                    this.orderInfo = '';
                    this.$message.info('已重置为原始顺序');
                },

                showCurrentOrder() {
                    const names = this.tableData.map((item, index) => `${index + 1}. ${item.name}`);
                    this.orderInfo = '当前顺序：' + names.join(' → ');
                }
            }
        });
    </script>
</body>

</html>