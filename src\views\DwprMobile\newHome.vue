<template>
    <div class="workplace-container">
     <!-- 主内容区域 -->
      <div class="content-wrapper" :class="{ 'with-tabbar': showTabbar }">
        <router-view />
      </div>
      <!-- 底部导航栏 -->
      <van-tabbar v-if="showTabbar" route>
        <van-tabbar-item replace to="/Dwpr/well">
          <template #icon="{ active }">
            <van-icon :name="active ? wellActiveIcon : wellIcon" />
          </template>
          Wells
        </van-tabbar-item>
        <van-tabbar-item replace to="/Dwpr/work">
          <template #icon="{ active }">
             <van-icon :name="active ? workActiveIcon : workIcon" />
          </template>
          Workbench
        </van-tabbar-item>
      </van-tabbar>
    </div>
  </template>
  
  <script setup lang="ts">
  import { computed } from 'vue'

  // 导入图片资源
  import wellIcon from '@/icon/dwprMobile/wells.svg'
  import wellActiveIcon from '@/icon/dwprMobile/wells-active.svg'
  import workIcon from '@/icon/dwprMobile/work.svg'
  import workActiveIcon from '@/icon/dwprMobile/work-active.svg'
  import { useShowTabbarStore } from '@/stores/useShowTabbar.ts'
  const showTabbarStore = useShowTabbarStore()
  const showTabbar = computed(() => showTabbarStore.showTabbar)

  </script>
  
  <style scoped lang="less">
  .workplace-container {
    width: 100vw;
    height: 100vh;
    height: 100dvh; // 动态视口高度，优先使用
    background-color: #EDEDED;
    display: flex;
    flex-direction: column;

     // 安全区域适配
    // padding-top: env(safe-area-inset-top);
    // padding-left: env(safe-area-inset-left);
    // padding-right: env(safe-area-inset-right);
  }
  // 自定义 tabbar 文字颜色
  :deep(.van-tabbar-item) {
    // 未激活状态的文字颜色
    --van-tabbar-item-text-color: #3D3D3D;
    // 激活状态的文字颜色
    --van-tabbar-item-active-color: #4472C4;
  }
  .content-wrapper {
    flex: 1;
    min-height: 0; // 重要：确保flex子项可以收缩
    overflow: hidden;
  }
  .with-tabbar {
    padding-bottom: 3.125rem;
  }
  // 不支持 dvh 的浏览器降级方案
  @supports not (height: 100dvh) {
    .workplace-container {
      height: 100vh;
    }
  }
  </style>
