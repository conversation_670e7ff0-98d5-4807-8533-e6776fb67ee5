<template>
  <!-- <el-button type="primary" @click="testMqtt" :loading="loading">testMqtt</el-button> -->
  <el-tree
    :data="treeData"
    :props="defaultProps"
    node-key="id"
    :default-expanded-keys="defaultExpandedKeys"
    highlight-current
    :expand-on-click-node="false"
    @node-click="handleNodeClick"
    @node-expand="handleNodeExpand"
    ref="tree"
    class="custom-tree"
    @node-contextmenu="handleContextMenu"
  >
    <template #default="{ node, data }">
      <!-- Python脚本节点 -->
      <span v-if="data.moduleType === 2 && data.isDataSetNode === 0" class="custom-tree-node">
        <img src="/src/icon/tree/python.svg" alt="Python" style="width: 15px; height: 15px;" />
        <span>{{ node.label }}</span>
      </span>
      <!-- 井节点 -->
      <span v-else-if="data.isDataSetNode === 1 && data.dataSetNodeType === 2" class="custom-tree-node">
        <img src="/src/icon/tree/Well.png" alt="Well" style="width: 15px; height: 15px;" />
        <span>{{ node.label }}</span>
      </span>
      <!-- 井眼节点 -->
      <span v-else-if="data.isDataSetNode === 1 && data.dataSetNodeType === 3" class="custom-tree-node">
        <img src="/src/icon/tree/Wellbore.png" alt="Wellbore" style="width: 15px; height: 15px;" />
        <span>{{ node.label }}</span>
      </span>
      <!-- 数据集节点 -->
      <span v-else-if="data.isDataSetNode === 1 && data.dataSetNodeType === 4" class="custom-tree-node">
        <img src="/src/icon/tree/data-set.png" alt="Dataset" style="width: 15px; height: 15px;" />
        <span>{{ node.label }}</span>
      </span>
      <!-- 曲线节点 -->
      <span v-else-if="data.isDataSetNode === 1 && data.dataSetNodeType === 5" class="custom-tree-node">
        <el-icon v-if="data.loading" class="is-loading" style="width: 15px; height: 15px;">
          <Loading />
        </el-icon>
        <img v-else :src="/src/icon/tree/channel.png" alt="Channel" style="width: 15px; height: 15px;" />
        <span>{{ data.loading ? 'Loading...' : node.label }}</span>
      </span>
      <!-- Wells文件夹节点 -->
      <span v-else-if="data.moduleType === 1 && data.isDataSetNode === 1 && data.dataSetNodeType === 0" class="custom-tree-node">
        <img src="/src/icon/tree/wells.svg" alt="Wells" style="width: 15px; height: 15px;" />
        <span>{{ node.label }}</span>
      </span>
      <!-- Scripts文件夹节点 -->
      <span v-else-if="data.moduleType === 1 && data.isDataSetNode === 0 && data.dataSetNodeType === null" class="custom-tree-node">
        <img src="/src/icon/tree/Folders.svg" alt="Scripts" style="width: 15px; height: 15px;" />
        <span>{{ node.label }}</span>
      </span>
      <!-- 普通文件夹节点 -->
      <span v-else-if="data.moduleType === 1" class="custom-tree-node">
        <el-icon :size="15">
          <Folder />
        </el-icon>
        <span>{{ node.label }}</span>
      </span>
      <span v-else-if="data.dataSetNodeType === 5 && data.loading" v-loading="data?.loading"></span>
      <!-- 默认节点 -->
      <span v-else class="custom-tree-node">
        <el-icon :size="15">
          <Coin />
        </el-icon>
        <span>{{ node.label }}</span>
      </span>
    </template>
  </el-tree>
  <!-- <ContextMenu 
    :menuItems="customMenuItems" 
    @menu-action="handleMenuAction"
    ref="contextMenu"
  /> -->
  <ContextMenu 
    :menuItems="customMenuItems" 
    @menu-action="handleMenuAction"
    ref="contextMenu"
  />
  <!-- 曲线预览弹框 -->
    <curvePreviewDialog ref="curvePreviewDialogRef" />
  <!-- <el-dialog
    :title=curveTitle
    draggable
    v-model="curvePreviewDialog"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleCurvePreviewDialogClose">
    <curvePreview />
  </el-dialog> -->

  <el-dialog
    title="Create Project"
    v-model="dialogVisible"
    width="550px"
    :close-on-click-modal="false"
  >
    <el-form :model="form" ref="formRef" :rules="rules">
      <el-form-item
        label="ProjectName"
        prop="projectName"
        class="form-item"
        label-width="120px"
      >
        <el-input
          v-model="form.projectName"
          placeholder="Please enter project name"
          class="input-field"
        ></el-input>
      </el-form-item>

      <el-form-item
        label="Remark"
        prop="note"
        class="form-item"
        label-width="120px"
      >
        <el-input
          v-model="form.note"
          placeholder="Please enter remark"
          class="input-field"
        ></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button
          type="primary"
          :disabled="isConfirmDisabled_createProject"
          @click="createProject"
          >Confirm</el-button
        >
      </div>
    </template>
  </el-dialog>

  <el-dialog
    title="Add Dataset"
    v-model="addDataSetDialogVisible"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form :model="dataSetFrom" ref="dataSetFrom" :rules="rules">
      <!-- 油田 -->
      <el-form-item label="Oilfield" class="form-item" label-width="80px">
        <el-select
          v-model="dataSetFrom.oilfield"
          placeholder="Please select oilfield"
          filterable
          allow-create
          :disabled="loading || oilFieldReadonly"
          @change="onOilfieldChange"
        >
          <el-option
            v-for="oilfield in dataSetFrom.oilfieldList"
            :key="oilfield.id"
            :label="oilfield.cOilFieldName"
            :value="oilfield.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 井 -->
      <el-form-item label="Well" class="form-item" label-width="80px">
        <el-select
          v-model="dataSetFrom.well"
          placeholder="Please select well"
          filterable
          allow-create
          :disabled="loading || wellReadonly"
          @change="onWellChange"
        >
          <el-option
            v-for="well in dataSetFrom.wellList"
            :key="well.id"
            :label="well.wellName"
            :value="well.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 井眼 -->
      <el-form-item label="Wellbore" class="form-item" label-width="80px">
        <el-select
          v-model="dataSetFrom.wellbore"
          placeholder="Please select wellbore"
          filterable
          allow-create
          :disabled="loading || wellboreReadonly"
          @change="onWellboreChange"
        >
          <el-option
            v-for="wellbore in dataSetFrom.wellboreList"
            :key="wellbore.id"
            :label="wellbore.wellboreNumber"
            :value="wellbore.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 数据集 -->
      <el-form-item label="Dateset" class="form-item" label-width="80px">
        <el-select
          v-model="dataSetFrom.dataSet"
          placeholder="Please select dataset"
          filterable
          allow-create
          :disabled="loading || dataSetReadonly"
          @change="onDataSetChange"
        >
          <el-option
            v-for="dataSet in dataSetFrom.dataSetList"
            :key="dataSet.id"
            :label="dataSet.name"
            :value="dataSet.id"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="addDataSetDialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="addDataSetNode" :loading="loading"
          >Confirm</el-button
        >
      </div>
    </template>
  </el-dialog>

  <el-dialog
    title="Code Samples"
    v-model="codeDialogVisible"
    width="900px"
    class="code-sample-dialog"
  >
    <el-row style="height: 650px">
      <el-col :span="8">
        <!-- 树形组件 -->
        <el-tree
          :data="treeDataForDialog"
          :props="defaultProps"
          node-key="id"
          default-expand-all
          highlight-current
          :expand-on-click-node="false"
          @node-click="handleTreeNodeClickInDialog"
          ref="treeDialog"
          class="custom-tree"
          :render-content="renderContent_dialog"
        >
          <template #default="{ node, data }">
            <span class="custom-tree-node">
              <el-icon v-if="data.moduleType_dialog === 1" :size="15">
                <Folder />
              </el-icon>
              <el-icon v-else :size="15">
                <Document />
              </el-icon>
              <span>{{ node.label }}</span>
            </span>
          </template>
        </el-tree>
      </el-col>
      <el-col :span="16">
        <!-- 文本框 -->
        <el-input
          type="textarea"
          :rows="30"
          v-model="selectedNodeText"
        ></el-input>
      </el-col>
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button @click="codeDialogVisible = false">Cancel</el-button>
      <el-button @click="copyCodeToEditor" type="primary">Confirm</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mqtt from "mqtt";
import axios from "axios";
import "element-plus/dist/index.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import ContextMenu from "./ContextMenu.vue";
import { useResourceTreeStore } from "@/stores/resourceTreeStore";
// import { curvePreview } from "@/api/curvePreview";
import curvePreview from "./curvePreview.vue";
import { usePreviewStore } from "@/stores/preview.js";
export default {
  components: {
    ContextMenu,
    curvePreview
  },
  name: "ResourceTree",
  props: {
    value: "",
  },
  emits: ["click"], // 声明 click 事件
  data() {
    return {
      client: null,//Mqtt客户端
      treeData: [], // 树形数据      
      defaultProps: {
        children: "children",
        label: "label",
      },
      form: {
        wellboreIds: [],
        projectName: "",
        note: "",
      },
      dataSetFrom: {
        oilfield: "",
        well: "",
        wellbore: "",
        dataSet: "",
        channel: "",
        oilfieldList: [],
        wellList: [],
        wellboreList: [],
        dataSetList: [],
        channelList: [],
      },
      rules: {
        //表单校验规则
        wellboreIds: [
          { required: true, message: "请选择井眼", trigger: "change" },
        ],
        projectName: [
          { required: true, message: "请输入工程名称", trigger: "blur" },
        ],
      },
      oilFieldReadonly: false,
      wellReadonly: false,
      wellboreReadonly: false,
      dataSetReadonly: false,
      channelReadonly: false,
      loading: false, // 用于控制加载状态
      currentNode: null,
      vWebApiUrl:
        window.location.protocol + "//" + window.location.host + "/api",
      dialogVisible: false, //创建工程弹框的控制变量
      addDataSetDialogVisible: false, // 数据集弹框的控制变量
      appId: "", // 用于获取当前应用的 ID
      codeDialogVisible: false, // 控制代码范例弹框的显示
      treeDataForDialog: [], // 用于存储对话框中的树形数据
      selectedNodeText: "",
      isConfirmDisabled_createProject: false,
      // curvePreviewDialog: false,
      userId: "", // 当前用户的ID
      defaultExpandedKeys: [], // 默认展开的节点ID数组
      customMenuItems: [
        {
          label: "Create",
          action: "create",
          children: [
            { label: "Create File", action: "add" },
            { label: "Create Folder", action: "addFolder" },
          ],
        },
        { label: "Edit Node", action: "edit" },
        { label: "Delete Node", action: "delete" },
        //{ label: "Refresh", action: "refresh" },
      ],
      curveIcon:''
      // curveTitle: ''
    };
  },

  async mounted() {
    const hash = window.location.hash;
    const params = new URLSearchParams(hash.split('?')[1]);
    this.appId = params.get("appId");
    this.id = params.get("id");
    if (!this.id) {
      this.showCreateProjectPrompt();
    } else {
      await this.loadTreeData();
      //this.create_tempfile();
    }
    const store = useResourceTreeStore();
    //连接mqtt服务
    this.connect();
    store.registerTreeInstance(this);
    this.curveIcon = new Image()
    this.curveIcon.src = '/src/icon/tree/channel.png'
  },
  methods: {
    // 计算默认展开的节点ID数组，排除 dataSetNodeType=4 的节点
    calculateDefaultExpandedKeys(treeData) {
      const expandedKeys = [];
      
      const traverse = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return;
        
        nodes.forEach(node => {
          // 添加调试信息
          console.log(`节点: ${node.label}, dataSetNodeType: ${node.dataSetNodeType}, isDataSetNode: ${node.isDataSetNode}`);
          
          // 只有当节点的 dataSetNodeType 明确等于 4 时，才不展开
          if (node.dataSetNodeType === 4) {
            console.log(`跳过展开节点: ${node.label}`);
            // 不添加到展开列表中，也不递归处理子节点（避免子节点展开导致父节点展开）
            // if (node.children && node.children.length > 0) {
            //   traverse(node.children);
            // }
          } else {
            // 其他所有节点都展开
            console.log(`添加展开节点: ${node.label}`);
            expandedKeys.push(node.id);
            
            // 递归处理子节点
            if (node.children && node.children.length > 0) {
              traverse(node.children);
            }
          }
        });
      };
      
      traverse(treeData);
      return expandedKeys;
    },
    
    async getCurrentUserId() {
      try {
        const response = await axios.get(
          `${this.vWebApiUrl}/sdk/pythonfreemodule/GetUserId`
        );

        if (!response.data.success) {
          throw new Error(
            `API 请求失败: ${response.data.message || "未知错误"}`
          );
        }

        this.userId = response.data.data;

        return this.userId; // 返回 Promise 解析值
      } catch (error) {
        console.error("获取用户ID时发生错误:", error);
        // 根据需要抛出错误或返回默认值
        throw error;
      }
    },
    // 获取当前节点的井节点ID
    getWellNodeId(node) {
      let currentNodeId = node.id;
      let currentNode = node;
      
      // 如果当前节点就是井节点，直接返回
      if (currentNode.moduleType === 1 && currentNode.dataSetNodeType === 2) {
        return currentNode.nodeDataId || currentNode.id;
      }
      
      // 向上遍历所有父节点查找井节点
      while (currentNodeId) {
        const parent = this.findParent(this.treeData, currentNodeId);
        if (parent) {
          // 检查父节点是否是井节点
          if (parent.moduleType === 1 && parent.dataSetNodeType === 2) {
            return parent.nodeDataId || parent.id;
          }
          currentNodeId = parent.id;
        } else {
          break;
        }
      }
      
      return null; // 没有找到井节点
    },
    handleContextMenu(event, node) {
      event.preventDefault();
      //console.log(node);
      //console.log(node.data.isDataSetNode);
      //console.log(node.data.dataSetNodeType);
      
      // 先重置菜单项，确保使用默认菜单
      this.$refs.contextMenu.resetMenuItems();

      // 检查当前节点是否为 wells 节点
      if (node && node.isDataSetNode === 1 && node.dataSetNodeType === 0) {
        console.log("wells 节点");
        // 创建只包含 Add DataSet 选项的菜单项
        const wellsMenuItems = [
          { label: "Add DataSet", action: "addDataSet" },
          { label: "Delete Node", action: "delete" }
        ];
        // 如果是 wells 节点，显示特殊的右键菜单（只有 Add DataSet 选项）
        this.showWellsContextMenu(event, node, wellsMenuItems);
        return;
      } else if (node && node.moduleType === 4 && node.dataSetNodeType === 5) {
        const nodeMenuItems = [
          { label: "Curve Preview", action: "curvePreview" }
        ];
        // 获取井节点ID
        const wellNodeId = this.getWellNodeId(node);
        // 获取数据集ID
        const ParentNode = this.findParent(this.treeData,node.id);
        const newNode = {
          ...node,
          wellNodeId: wellNodeId,
          datasetId: ParentNode.nodeDataId,
        }
        console.log(newNode, 'newNode')
        this.showWellsContextMenu(event, newNode, nodeMenuItems);
        return;
      }
      
      // 检查是否为其他数据集节点，屏蔽右键菜单
      if (node && node.isDataSetNode === 1) {
        // 屏蔽其他数据集节点的右键菜单
        return;
      }
      
      // 显示默认右键菜单
      this.$refs.contextMenu.show(event, node);
    },
    
    // 显示 wells 节点的特殊右键菜单
    showWellsContextMenu(event, node, menu) {
      // 使用新方法设置菜单项并显示
      this.$refs.contextMenu.setMenuItems(menu);
      this.$refs.contextMenu.show(event, node);
    },
    handleMenuAction(action, node) {
      this.currentNode = node;
      switch (action) {
        case "add":
          this.addNewNode("文件", 2);
          break;
        case "addFolder":
          this.addNewNode("文件夹", 1);
          break;
        case "edit":
          this.renameNode();
          break;
        case "curvePreview":
          this.showCurvePrivew(node);
          break;
        case "delete":
          this.deleteNode();
          break;
        case "refresh":
          if (
            this.currentNode.isDataSetNode === 1 &&
            this.currentNode.dataSetNodeType === 4
          ) {
            const parentNode = this.findParent(
              this.treeData,
              this.currentNode.id
            );
            this.getChannels(
              this.currentNode.nodeDataId,
              parentNode.nodeDataId,
              this.currentNode
            );
          }
          break;
        case "addDataSet":
          this.addDataSet();
          break;
      }
    },
    //加载/刷新 数据集节点下的曲线数据
    loadChannels(data) {
      if (data.isDataSetNode === 1 && data.dataSetNodeType === 4) {
        // 在开始加载前，先显示加载状态
        data.children = [{
          id: this.generateGUID(),
          label: 'Loading...',
          children: [],
          moduleType: 4,
          isDataSetNode: 1,
          dataSetNodeType: 5,
          nodeDataId: '',
          loading: true
        }];

        const parentNode = this.findParent(this.treeData, data.id);
        this.getChannels(data.nodeDataId, parentNode.nodeDataId, data);
      }
    },
    handleAction(action) {
      switch (action) {
        case "createProject":
          window.open(
            window.location.protocol +
              "//" +
              window.location.host +
              "/static/app/#/PythonFreeModule/?appId=" +
              this.appId
          );
          break;
        case "newFolder":
          this.addNewNode("文件夹", 1);
          break;
        case "newFile":
          this.addNewNode("文件", 2);
          break;
        case "delete":
          this.deleteNode();
          break;
        case "rename":
          this.renameNode();
          break;
        case "refresh":
          this.refreshTree();
          break;
        case "addDataSet":
          this.addDataSet();
          break;
        case "codeSample":
          this.showCodeSampleDialog();
          break;
        default:
          console.log(action + "操作...");
          break;
      }
    },
    // 添加新节点
    addNewNode(type, moduleType) {
      if (this.currentNode && this.currentNode.isDataSetNode !== 1) {
        const parentNode = this.findParent(this.treeData, this.currentNode.id);

        this.$prompt("Please input the name of the new node", "Create", {
          confirmButtonText: "Ok",
          cancelButtonText: "Cancel",
          inputValue: type === "文件夹" ? "New Folder" : "NewScript.py",
        })
          .then(({ value }) => {
            const newNode = {
              id: this.generateGUID(),
              label: value,
              children: [],
              moduleType: moduleType, // 设置 moduleType 属性
              isDataSetNode: 0, // 设置 isDataSetNode 属性 用于判断是数据集节点还是文件节点
              pyModuleId: "", //对应的python脚本id
              script: "", //当前节点的脚本内容
            };

            // 确保新建文件名默认以 .py 结尾
            if (moduleType === 2 && !value.endsWith(".py")) {
              value += ".py";
            }
            // 根据当前节点的类型添加新节点
            if (this.currentNode.moduleType === 2) {
              // 新节点需要添加到父节点中
              if (parentNode) {
                parentNode.children.push(newNode);
              } else {
                console.log("无法找到父节点");
                return;
              }
            } else {
              // 在当前节点的 children 数组中添加新节点
              this.currentNode.children.push(newNode);
            }
            this.treeData = JSON.parse(JSON.stringify(this.treeData));
            console.log("当前树结构");
            console.log(this.treeData);

            //保存结构至数据库
            this.$nextTick(() => {
              const hash = window.location.hash;
              const params = new URLSearchParams(hash.split('?')[1]);
              const projectId = params.get("id");

              axios
                .post(`${this.vWebApiUrl}/project/project/SaveTreeForPythonApp`, {
                  appId: this.appId,
                  projectId: projectId,
                  treeData: this.treeData,
                })
                .then((response) => {
                  if (response.data.success) {
                    const data = response.data.data;
                    const currentPath = window.location.hash.split('?')[0];
                    const newHash = `${currentPath}?id=${data.id}&appId=${this.appId}`;
                    window.location.hash = newHash;
                    const profileData = JSON.parse(response.data.data.profile); // 正确解析 profile 数据
                    this.treeData = profileData; // 更新树形数据
                    
                    // 计算默认展开的节点，排除 dataSetNodeType=4 的节点
                    this.defaultExpandedKeys = this.calculateDefaultExpandedKeys(this.treeData);

                    //新节点添加后，默认选中新节点
                    this.$nextTick(() => {
                      if (this.treeData.length > 0) {
                        this.$refs.tree.setCurrentKey(newNode.id);
                        this.currentNode = this.findNodeById(
                          this.treeData,
                          newNode.id
                        );
                        //this.$refs.editor.addTab(newNode.id,newNode.label, ''); // 调用 addTab 方法
                        //新增节点为脚本节点时才触发
                        if (this.currentNode.moduleType === 2) {
                          this.$emit("click", " ", newNode.id, newNode.label);
                        }
                      } else {
                        this.$message.warning(
                          "Tree data is empty, unable to select node."
                        );
                      }
                    });
                  }
                })
                .catch((error) => {
                  console.error("创建项目时出错:", error);
                })
                .finally(() => {
                  this.dialogVisible = false;
                  this.resetForm();
                });
            });

            console.log("新节点已生成");
          })
          .catch((e) => {
            console.log(e);
          });
      } else {
        if (!this.currentNode) {
          this.$message.warning("Please select a node");
        }
        if (this.currentNode.isDataSetNode === 1) {
          this.$message.warning("Please select a node under the Scripts");
        }
      }
    },
    // 执行删除节点
    deleteNode() {
      if (this.currentNode && this.currentNode.id !== this.treeData[0].id) {
        this.$confirm("确定要删除该节点?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            // 获取要删除的 pyModuleId
            const pyModuleIds = this.getPyModuleIds(this.currentNode);

            // 调用后端接口删除文件
            if (pyModuleIds.length > 0) {
              axios
                .post(`${this.vWebApiUrl}/sdk/pythonfreemodule/DeleteFiles`, {
                  pyModuleIds: pyModuleIds,
                })
                .then((response) => {
                  if (response.data.success) {
                    console.log(response.data);
                    //this.$message.success('文件删除成功');
                  } else {
                    console.log(response.data);
                  }
                })
                .catch((error) => {
                  console.error("删除文件时出错:", error);
                });
            }

            // 找到当前节点的父节点
            const parent = this.findParent(this.treeData, this.currentNode.id);
            if (parent) {
              // 从父节点的 children 数组中删除当前节点
              const index = parent.children.findIndex(
                (child) => child.id === this.currentNode.id
              );
              if (index !== -1) {
                parent.children.splice(index, 1);
              }
            }

            const hash = window.location.hash;
            const params = new URLSearchParams(hash.split('?')[1]);
            const projectId = params.get("id");

            axios
              .post(`${this.vWebApiUrl}/project/project/SaveTreeForPythonApp`, {
                appId: this.appId,
                projectId: projectId,
                treeData: this.treeData,
              })
              .then((response) => {
                this.$message.success(response.data.message);
                if (response.data.success) {
                  const data = response.data.data;
                  const currentUrl = new URL(window.location.href);
                  currentUrl.searchParams.set("id", data.id);
                  window.history.pushState({}, "", currentUrl.toString());
                  this.treeData = JSON.parse(data.profile);
                  
                  // 计算默认展开的节点，排除 dataSetNodeType=4 的节点
                  this.defaultExpandedKeys = this.calculateDefaultExpandedKeys(this.treeData);

                  //this.$refs.editor.closeTab(this.currentNode.id); // 调用 closeTab 方法

                  // 新节点添加后，默认选中新节点
                  this.$nextTick(() => {
                    if (this.treeData.length > 0) {
                      this.$refs.tree.setCurrentKey(this.treeData[0].id);
                      this.currentNode = this.treeData[0];
                    } else {
                      this.$message.warning("删除失败");
                    }
                  });
                }
              })
              .catch((error) => {
                console.error("创建项目时出错:", error);
              });

            console.log("已删除节点");
          })
          .catch(() => {
            console.log("取消删除");
          });
      } else {
        this.$message.warning("Cannot delete the root node.");
        return;
      }
    },
    // 重命名节点
    renameNode() {
      if (this.currentNode && this.currentNode.isDataSetNode !== 1) {
        this.$prompt("Please input the new name of the node", "Tips", {
          confirmButtonText: "Ok",
          cancelButtonText: "Cancel",
          inputValue: this.currentNode.label,
        })
          .then(({ value }) => {
            const hash = window.location.hash;
            const params = new URLSearchParams(hash.split('?')[1]);
            const projectId = params.get("id");

            this.currentNode.label = value;
            axios
              .post(`${this.vWebApiUrl}/project/project/SaveTreeForPythonApp`, {
                appId: this.appId,
                projectId: projectId,
                treeData: this.treeData,
              })
              .then((response) => {
                this.$message.success(response.data.message);
                if (response.data.success) {
                  const data = response.data.data;
                  const currentUrl = new URL(window.location.href);
                  currentUrl.searchParams.set("id", data.id);
                  window.history.pushState({}, "", currentUrl.toString());
                  this.treeData = JSON.parse(data.profile);
                  
                  // 计算默认展开的节点，排除 dataSetNodeType=4 的节点
                  this.defaultExpandedKeys = this.calculateDefaultExpandedKeys(this.treeData);

                  //节点修改后，默认选中修改过的节点
                  this.$nextTick(() => {
                    if (this.treeData.length > 0) {
                      this.$refs.tree.setCurrentKey(this.currentNode.id);
                    } else {
                      this.$message.warning(
                        "tree data is empty，cannot select node."
                      );
                    }
                  });
                }
              });
            console.log("节点已重命名");
          })
          .catch(() => {
            console.log("取消重命名");
          });
      } else {
        if (this.currentNode.isDataSetNode === 1) {
          this.$message.warning("Please select a file node under the Scripts");
        }
        console.log("未选择节点");
      }
    },
    // 刷新树
    refreshTree() {
      this.loadTreeData();
    },
    //询问是否创建工程
    showCreateProjectPrompt() {
      this.$msgbox({
        //message: "未找到工程，是不是创建新工程？",
        message: "No project found, do you want to create a new project?",
        title: "Tips",
        showCancelButton: true,
        closeOnClickModal: false,
        // confirmButtonText: "确定",
        // cancelButtonText: "取消",
        confirmButtonText: "Confirm",
        cancelButtonText: "Cancel",
      })
        .then(() => {
          this.dialogVisible = true; // 显示创建工程的对话框
        })
        .catch(() => {
          console.log("用户选择取消");
        });
    },
    // 创建工程
    createProject() {
      this.isConfirmDisabled_createProject = true;
      // 处理创建工程逻辑
      const { projectName, note } = this.form; // 解构获取 projectName 和 note
      const hash = window.location.hash;
      const params = new URLSearchParams(hash.split('?')[1]);
      const appId = params.get("appId");
      
      console.log("创建的hash:", params);
      console.log("创建的工程信息:", this.form);
      console.log("创建的appid:", appId);

      axios
        .post(
          `${this.vWebApiUrl}/project/project/CreateProject?appId=${appId}&projectName=${projectName}&note=${note}`
        )
        .then((response) => {
          this.$message.success(response.data.message);
          if (response.data.success) {
            const profileData = JSON.parse(response.data.data.profile); // 正确解析 profile 数据
            this.treeData = profileData; // 更新树形数据
            
            // 计算默认展开的节点，排除 dataSetNodeType=4 的节点
            this.defaultExpandedKeys = this.calculateDefaultExpandedKeys(this.treeData);
            
            const data = response.data.data;
            // const currentUrl = new URL(window.location.href);
            // currentUrl.searchParams.set("id", data.id);
            // window.history.pushState({}, "", currentUrl.toString());
            
            const currentPath = window.location.hash.split('?')[0];
            const newHash = `${currentPath}?id=${data.id}&appId=${this.appId}`;
            window.location.hash = newHash;


            //创建成功后，选中第一个节点
            this.$nextTick(() => {
              if (this.treeData.length > 0) {
                this.$refs.tree.setCurrentKey(this.treeData[0].id);
                this.currentNode = this.treeData[0];
              } else {
                this.$message.warning("树形数据为空，无法选中节点");
              }
            });
          }
        })
        .catch((error) => {
          console.error("创建项目时出错:", error);
        })
        .finally(() => {
          this.dialogVisible = false;
          this.isConfirmDisabled_createProject = false;
          this.resetForm();
        });
    },
    resetForm() {
      this.form.projectName = "";
      this.form.note = "";
    },
    // 加载树形数据
    async loadTreeData() {
      const hash = window.location.hash;
      const params = new URLSearchParams(hash.split('?')[1]);
      this.appId = params.get("appId");
      const projectId = params.get("id");

      await axios
        .get(
          `${this.vWebApiUrl}/project/project/GetTree?appId=${this.appId}&projectId=${projectId}`
        )
        .then((response) => {
          if (response.data.success) {
            try {
              const profileData = JSON.parse(response.data.data.profile); // 正确解析 profile 数据
              this.treeData = profileData; // 更新树形数据
              
              // 计算默认展开的节点，排除 dataSetNodeType=4 的节点
              this.defaultExpandedKeys = this.calculateDefaultExpandedKeys(this.treeData);
              
              this.$nextTick(() => {
                // 默认选择第一个节点，如果存在的话
                if (this.treeData.length > 0) {
                  this.$refs.tree.setCurrentKey(this.treeData[0].id); // 设置树形控件当前选中节点
                  this.currentNode = this.treeData[0]; // 更新当前节点
                } else {
                  //this.$message.warning("树形数据为空，无法选中节点");
                  this.$message.warning("Tree data is empty, cannot select node");
                }
              });
            } catch (error) {
              console.error("解析 profile JSON 失败:", error);
              this.$message.error("无法解析树形数据，请检查返回的格式");
            }
          } else {
           // this.$message.error("加载树形数据失败: " + response.data.message);
           this.$message.error("Load tree data failed: " + response.data.message);
          }
        })
        .catch((error) => {
          console.error("请求树形数据时出错:", error);
        });
    },
    // 处理节点点击事件
    handleNodeClick(data) {
      console.log("点击节点:", data);
      this.currentNode = data;
      this.$refs.tree.setCurrentKey(data.id);
      if (data.moduleType === 2 && data.isDataSetNode === 0) {
        // 获取当前节点的 python 脚本 id
        const pyModuleId = data.pyModuleId;
        if (pyModuleId) {
          // 获取 python 脚本内容
          axios
            .get(
              `${this.vWebApiUrl}/sdk/pythonfreemodule/GetPyModuleContent?appId=${this.appId}&pyModuleId=${pyModuleId}`
            )
            .then((response) => {
              if (response.data.success) {
                try {
                  const content = response.data.data.script; // 正确解析 profile 数据
                  this.$emit("click", content, data.id, data.label);
                } catch (error) {
                  console.error("解析 Python 脚本内容失败:", error);
                  //this.$message.error("解析 Python 脚本内容失败，请检查格式");
                  this.$message.error("Parse Python script content failed, please check the format");
                }
              } else {
                // this.$message.error(
                //   "获取 Python 脚本内容失败: " + response.data.message
                // );
                this.$message.error("Get Python script content failed: " + response.data.message);
              }
            })
            .catch((error) => {
              console.error("请求 Python 脚本内容时出错:", error);
            });
        } else {
          this.$emit("click", " ", data.id, data.label);
        }
      } else {
        // 文件夹节点，不处理
        console.log("文件夹节点...");
      }
    },
    // 处理节点展开事件
    handleNodeExpand(data, node) {
      console.log("展开节点:", data);
      // 当展开数据集节点时，触发 loadChannels 方法
      if(data.isDataSetNode === 1 && data.dataSetNodeType === 4){
        this.loadChannels(data);
      }
    },
    showCurvePrivew(node) {
      return this.$refs.curvePreviewDialogRef.handleCurvePreviewDialogOpen(`${node.label} Preview`, node);
    },
    handleCurvePreviewDialogClose() {
      // 关闭预览弹窗时，重置预览状态
    },
    resetForm() {
      this.form.projectName = "";
      this.form.note = "";
    },
    // 新增数据集弹框
    addDataSet() {
      if (!this.currentNode) {
        this.$message.warning("Please select a node");
        return;
      }

      if (this.currentNode.isDataSetNode !== 1) {
        this.$message.warning("Please select a node under the Wells");
        return;
      }
      this.addDataSetDialogVisible = true; // 显示选择井眼弹框

      this.loading = true; // 开启加载状态
      // 根据当前节点的类型加载数据
      switch (this.currentNode.dataSetNodeType) {
        case 0: // 数据文件夹节点
          this.loadOilFields();
          break;
        case 1: // 油田节点
          this.loadOilFieldData();
          break;
        case 2: // 井节点
          this.loadWellData();
          break;
        case 3: // 井眼节点
          this.loadWellboreData();
          break;
        case 4: // 数据集节点
          this.loadWellboreData();
          this.currentNode = this.findParent(
            this.treeData,
            this.currentNode.id
          );
          break;
        default:
          this.$message.error("未知的节点类型");
      }
    },

    // 加载所有油田数据
    loadOilFields() {
      axios
        .get(`${this.vWebApiUrl}/oil/oilfield/ListAll`)
        .then((response) => {
          if (response.data.success) {
            this.dataSetFrom.oilfieldList = response.data.data;
            this.dataSetFrom.oilfield =
              this.dataSetFrom.oilfieldList[0]?.id || "";
            this.setDropdownStates(false, false, false, false);
            this.getWells(this.dataSetFrom.oilfield);
          } else {
            this.$message.error(
              "Get oilfield data failed: " + response.data.message
            );
          }
        })
        .catch((error) => {
          console.error("Get oilfield data failed:", error);
        })
        .finally(() => {
          this.loading = false; // 关闭加载状态
        });
    },

    // 加载油田节点数据
    loadOilFieldData() {
      axios
        .get(`${this.vWebApiUrl}/oil/oilfield/ListAll`)
        .then((response) => {
          if (response.data.success) {
            this.dataSetFrom.oilfieldList = response.data.data.filter(
              (item) => item.id === this.currentNode.nodeDataId
            );
            this.dataSetFrom.oilfield =
              this.dataSetFrom.oilfieldList[0]?.id || "";
            this.setDropdownStates(true, false, false, false);
            this.getWells(this.dataSetFrom.oilfield);
          } else {
            this.$message.error(
              "Get oilfield data failed: " + response.data.message
            );
          }
        })
        .catch((error) => {
          console.error("Get oilfield data failed:", error);
        });
    },

    // 加载井节点数据
    loadWellData() {
      axios
        .get(`${this.vWebApiUrl}/oil/oilfield/ListAll`)
        .then((response) => {
          if (response.data.success) {
            this.dataSetFrom.oilfieldList = response.data.data.filter(
              (item) => item.id === this.currentNode.oilfieldId
            );
            this.dataSetFrom.oilfield =
              this.dataSetFrom.oilfieldList[0]?.id || "";
            return this.getWells(this.dataSetFrom.oilfield);
          } else {
            this.$message.error(
              "Get well node data failed: " + response.data.message
            );
            throw new Error("Get well node data failed");
          }
        })
        .then(() => {
          this.dataSetFrom.wellList = this.dataSetFrom.wellList.filter(
            (item) => item.id === this.currentNode.nodeDataId
          );
          this.dataSetFrom.well = this.dataSetFrom.wellList[0]?.id || "";
          this.setDropdownStates(true, true, false, false);
          return this.getWellbores(this.dataSetFrom.well);
        })
        .catch((error) => {
          console.error("Get well node data failed:", error);
        })
        .finally(() => {
          this.loading = false; // 关闭加载状态
        });
    },

    // 加载井眼节点数据
    async loadWellboreData() {
      try {
        const response = await axios.get(
          `${this.vWebApiUrl}/oil/oilfield/ListAll`
        );
        if (response.data.success) {
          const parentNode = this.findParent(
            this.treeData,
            this.currentNode.id
          );
          this.dataSetFrom.oilfieldList = response.data.data.filter(
            (item) => item.id === parentNode.oilfieldId
          );
          this.dataSetFrom.oilfield =
            this.dataSetFrom.oilfieldList[0]?.id || "";
          await this.getWells(this.dataSetFrom.oilfield);
          this.dataSetFrom.wellList = this.dataSetFrom.wellList.filter(
            (item) => item.id === parentNode.nodeDataId
          );
          this.dataSetFrom.well = this.dataSetFrom.wellList[0]?.id || "";
          this.dataSetFrom.wellboreList = this.dataSetFrom.wellboreList.filter(
            (item) => item.id === this.currentNode.nodeDataId
          );
          this.dataSetFrom.wellbore =
            this.dataSetFrom.wellboreList[0]?.id || "";
          this.setDropdownStates(true, true, true, false);
        } else {
          this.$message.error(
            "get wellbore node data failed: " + response.data.message
          );
        }
      } catch (error) {
        console.error("get wellbore node data failed:", error);
      } finally {
        this.loading = false; // 关闭加载状态
      }
    },
    // 加载数据集节点数据
    loadDataSetData() {
      axios
        .get(`${this.vWebApiUrl}/oil/oilfield/ListAll`)
        .then((response) => {
          if (response.data.success) {
            const parentNode = this.findParent(
              this.treeData,
              this.currentNode.id
            );
            const grandParentNode = this.findParent(
              this.treeData,
              parentNode.id
            );
            const greatGrandParentNode = this.findParent(
              this.treeData,
              grandParentNode.id
            );
            this.dataSetFrom.oilfieldList = response.data.data.filter(
              (item) => item.id === greatGrandParentNode.nodeDataId
            );
            this.dataSetFrom.oilfield =
              this.dataSetFrom.oilfieldList[0]?.id || "";
            this.dataSetFrom.wellList = this.dataSetFrom.wellList.filter(
              (item) => item.id === grandParentNode.nodeDataId
            );
            this.dataSetFrom.well = this.dataSetFrom.wellList[0]?.id || "";
            this.dataSetFrom.wellboreList =
              this.dataSetFrom.wellboreList.filter(
                (item) => item.id === parentNode.nodeDataId
              );
            this.dataSetFrom.wellbore =
              this.dataSetFrom.wellboreList[0]?.id || "";
            this.dataSetFrom.dataSetList = this.dataSetFrom.dataSetList.filter(
              (item) => item.id === this.currentNode.nodeDataId
            );
            this.dataSetFrom.dataSet =
              this.dataSetFrom.dataSetList[0]?.id || "";
            this.setDropdownStates(true, true);
          } else {
            this.$message.error(
              "get dataset node data failed: " + response.data.message
            );
          }
        })
        .catch((error) => {
          console.error("get dataset node data failed:", error);
        });
    },

    // 设置下拉框的只读状态
    setDropdownStates(
      oilFieldReadonly,
      wellReadonly,
      wellboreReadonly,
      dataSetReadonly
    ) {
      this.oilFieldReadonly = oilFieldReadonly;
      this.wellReadonly = wellReadonly;
      this.wellboreReadonly = wellboreReadonly;
      this.dataSetReadonly = dataSetReadonly;
    },

    // 查找现有的井节点
    findExistingWellNode(wellId) {
      const wellsNode = this.findWellsNode();
      console.log('查找井节点 - wellsNode:', wellsNode);
      console.log('查找井节点 - wellId:', wellId, '类型:', typeof wellId);
      
      if (!wellsNode || !wellsNode.children) {
        console.log('Wells节点不存在或没有子节点');
        return null;
      }
      
      const existingWell = wellsNode.children.find(node => {
        console.log('检查节点:', node.label, 'nodeDataId:', node.nodeDataId, '类型:', typeof node.nodeDataId);
        return node.isDataSetNode === 1 && 
               node.dataSetNodeType === 2 && 
               String(node.nodeDataId) === String(wellId);
      });
      
      console.log('找到的井节点:', existingWell);
      return existingWell;
    },

    // 查找现有的井眼节点
    findExistingWellboreNode(wellboreId, wellNode) {
      console.log('查找井眼节点 - wellboreId:', wellboreId, '类型:', typeof wellboreId);
      console.log('查找井眼节点 - wellNode:', wellNode);
      
      if (!wellNode || !wellNode.children) {
        console.log('井节点不存在或没有子节点');
        return null;
      }
      
      const existingWellbore = wellNode.children.find(node => {
        console.log('检查井眼节点:', node.label, 'nodeDataId:', node.nodeDataId, '类型:', typeof node.nodeDataId);
        return node.isDataSetNode === 1 && 
               node.dataSetNodeType === 3 && 
               String(node.nodeDataId) === String(wellboreId);
      });
      
      console.log('找到的井眼节点:', existingWellbore);
      return existingWellbore;
    },

    // 查找Wells根节点
    findWellsNode() {
      // 首先在根级别查找
      let wellsNode = this.treeData.find(node => 
        node.isDataSetNode === 1 && 
        node.dataSetNodeType === 0
      );
      
      // 如果在根级别没找到，递归查找所有子节点
      if (!wellsNode) {
        const findWellsInChildren = (nodes) => {
          for (const node of nodes) {
            if (node.isDataSetNode === 1 && node.dataSetNodeType === 0) {
              return node;
            }
            if (node.children && node.children.length > 0) {
              const found = findWellsInChildren(node.children);
              if (found) return found;
            }
          }
          return null;
        };
        
        wellsNode = findWellsInChildren(this.treeData);
      }
      
      console.log('找到的Wells根节点:', wellsNode);
      return wellsNode;
    },

    //新增数据集节点
    addDataSetNode() {
      if (
        this.dataSetFrom.dataSetList.length > 0 &&
        this.dataSetFrom.wellboreList.length > 0 &&
        this.dataSetFrom.wellboreList.length > 0
      ) {
        const selectedWellId = this.dataSetFrom.well;
        const selectedWellboreId = this.dataSetFrom.wellbore;
        const selectedDataSetId = this.dataSetFrom.dataSet;
        
        console.log('=== 开始添加数据集节点 ===');
        console.log('选择的井ID:', selectedWellId, '类型:', typeof selectedWellId);
        console.log('选择的井眼ID:', selectedWellboreId, '类型:', typeof selectedWellboreId);
        console.log('选择的数据集ID:', selectedDataSetId, '类型:', typeof selectedDataSetId);
        
        // 检查是否已存在相同的井节点
        let existingWellNode = this.findExistingWellNode(selectedWellId);
        let existingWellboreNode = null;
        
        if (existingWellNode) {
          console.log('找到现有井节点:', existingWellNode.label);
          // 如果井节点存在，检查是否已存在相同的井眼节点
          existingWellboreNode = this.findExistingWellboreNode(selectedWellboreId, existingWellNode);
          if (existingWellboreNode) {
            console.log('找到现有井眼节点:', existingWellboreNode.label);
          } else {
            console.log('未找到现有井眼节点');
          }
        } else {
          console.log('未找到现有井节点');
        }

        const fakerChannelNode = {
          id: this.generateGUID(),
          label: "",
          children: [],  //虚假数据，用于占位
          moduleType: 4, // 设置 moduleType 属性
          isDataSetNode: 1, // 设置 isDataSetNode 属性 用于判断是数据集节点还是文件节点
          dataSetNodeType: 5, //0-数据文件夹的根目录 1-油田，2-井，3-井眼,4-数据集,5-曲线
        };

        const datasets = {
          id: this.generateGUID(),
          label: this.dataSetFrom.dataSetList.filter(
            (item) => item.id === selectedDataSetId
          )[0].name,
          nodeDataId: selectedDataSetId,
          children: [],
          moduleType: 4, // 设置 moduleType 属性
          isDataSetNode: 1, // 设置 isDataSetNode 属性 用于判断是数据集节点还是文件节点
          dataSetNodeType: 4, //0-数据文件夹的根目录 1-油田，2-井，3-井眼,4-数据集,5-曲线
          expanded: false,
        };

        if (this.currentNode.isDataSetNode === 1) {
          if (this.currentNode.dataSetNodeType == 0) {
            // 在Wells根节点下添加
            if (existingWellNode) {
              // 井节点已存在
              if (existingWellboreNode) {
                // 井眼节点也存在，直接在井眼节点下添加数据集
                datasets.children.push(fakerChannelNode);
                existingWellboreNode.children.push(datasets);
              } else {
                // 井节点存在但井眼节点不存在，在井节点下添加井眼和数据集
                const wellbores = {
                  id: this.generateGUID(),
                  label: this.dataSetFrom.wellboreList.filter(
                    (item) => item.id === selectedWellboreId
                  )[0].wellboreNumber,
                  nodeDataId: selectedWellboreId,
                  children: [datasets],
                  moduleType: 1,
                  isDataSetNode: 1,
                  dataSetNodeType: 3,
                };
                datasets.children.push(fakerChannelNode);
                existingWellNode.children.push(wellbores);
              }
            } else {
              // 井节点不存在，创建完整的井-井眼-数据集结构
              const wellbores = {
                id: this.generateGUID(),
                label: this.dataSetFrom.wellboreList.filter(
                  (item) => item.id === selectedWellboreId
                )[0].wellboreNumber,
                nodeDataId: selectedWellboreId,
                children: [datasets],
                moduleType: 1,
                isDataSetNode: 1,
                dataSetNodeType: 3,
              };

              const wells = {
                id: this.generateGUID(),
                label: this.dataSetFrom.wellList.filter(
                  (item) => item.id === selectedWellId
                )[0].wellName,
                nodeDataId: selectedWellId,
                oilfieldId: this.dataSetFrom.wellList.filter(
                  (item) => item.id === selectedWellId
                )[0].oilFieldId,
                children: [wellbores],
                moduleType: 1,
                isDataSetNode: 1,
                dataSetNodeType: 2,
              };
              
              datasets.children.push(fakerChannelNode);
              this.currentNode.children.push(wells);
            }
          } else if (this.currentNode.dataSetNodeType == 2) {
            // 在井节点下添加
            if (existingWellboreNode) {
              // 井眼节点已存在，直接在井眼节点下添加数据集
              datasets.children.push(fakerChannelNode);
              existingWellboreNode.children.push(datasets);
            } else {
              // 井眼节点不存在，在井节点下添加井眼和数据集
              const wellbores = {
                id: this.generateGUID(),
                label: this.dataSetFrom.wellboreList.filter(
                  (item) => item.id === selectedWellboreId
                )[0].wellboreNumber,
                nodeDataId: selectedWellboreId,
                children: [datasets],
                moduleType: 1,
                isDataSetNode: 1,
                dataSetNodeType: 3,
              };
              datasets.children.push(fakerChannelNode);
              this.currentNode.children.push(wellbores);
            }
          } else if (this.currentNode.dataSetNodeType == 3) {
            // 在井眼节点下直接添加数据集
            datasets.children.push(fakerChannelNode);
            this.currentNode.children.push(datasets);
          }

          //保存结构至数据库
          this.$nextTick(() => {
            const hash = window.location.hash;
            const params = new URLSearchParams(hash.split('?')[1]);
            const projectId = params.get("id");

            axios
              .post(`${this.vWebApiUrl}/project/project/SaveTreeForPythonApp`, {
                appId: this.appId,
                projectId: projectId,
                treeData: this.treeData,
              })
              .then((response) => {
                this.$message.success(response.data.message);
                if (response.data.success) {
                  const data = response.data.data;
                  const currentUrl = new URL(window.location.href);
                  currentUrl.searchParams.set("id", data.id);
                  window.history.pushState({}, "", currentUrl.toString());
                  const profileData = JSON.parse(response.data.data.profile); // 正确解析 profile 数据
                  this.treeData = profileData; // 更新树形数据
                  
                  // 计算默认展开的节点，排除 dataSetNodeType=4 的节点
                  this.defaultExpandedKeys = this.calculateDefaultExpandedKeys(this.treeData);
                  this.$refs.tree.setCurrentKey(
                    this.treeData[0].children[1].id
                  );
                  this.currentNode = this.treeData[0].children[1];
                }
              })
              .catch((error) => {
                console.error("添加数据集节点时出错:", error);
              })
              .finally(() => {
                this.addDataSetDialogVisible = false;
              });
          });

          console.log(this.treeData);
        } else {
          this.$message.warning("please select a node under the Wells");
          return;
        }
      }
    },

    onOilfieldChange(newOilfieldId) {
      console.log("油田变化:", newOilfieldId);
      this.dataSetFrom.well = ""; // 清空当前井的选择
      this.dataSetFrom.wellbore = ""; // 清空当前井眼的选择
      this.dataSetFrom.dataSet = ""; // 清空当前数据集的选择
      this.getWells(newOilfieldId); // 重新获取井数据
    },

    onWellChange(newWellId) {
      console.log("井变化:", newWellId);
      this.dataSetFrom.wellbore = ""; // 清空当前井眼的选择
      this.dataSetFrom.dataSet = ""; // 清空当前数据集的选择
      this.getWellbores(newWellId); // 重新获取井眼数据
    },

    onWellboreChange(newWellboreId) {
      console.log("井眼变化:", newWellboreId);
      this.getDataSets(newWellboreId); // 重新获取数据集数据
    },

    onDataSetChange(newDataSetId) {
      console.log("数据集变化:", newDataSetId);
    },

    async getWells(oilfieldId) {
      try {
        const response = await axios.get(
          `${this.vWebApiUrl}/oil/oilwell/GetWellListAllByOilFieldId?oilfieldId=${oilfieldId}`
        );
        if (response.data.success) {
          this.dataSetFrom.wellList = response.data.data; // 存储井数据
          this.dataSetFrom.well = this.dataSetFrom.wellList[0]?.id || ""; // 默认选择第一个井
          // 获取井眼数据
          if (this.currentNode.dataSetNodeType === 3) {
            const parentNode = this.findParent(
              this.treeData,
              this.currentNode.id
            );
            await this.getWellbores(parentNode.nodeDataId);
          } else {
            await this.getWellbores(this.dataSetFrom.well);
          }
        } else {
          this.$message.error("get well data failed: " + response.data.message);
          throw new Error("get well data failed: " + response.data.message);
        }
      } catch (error) {
        console.error("get well data failed:", error);
        throw error;
      } finally {
        this.loading = false; // 关闭加载状态
      }
    },

    getWellbores(wellId) {
      return axios
        .get(
          `${this.vWebApiUrl}/oil/oilwellbore/GetWellboreByWellId?wellId=${wellId}`
        )
        .then((response) => {
          if (response.data.success) {
            this.dataSetFrom.wellboreList = response.data.data; // 存储井眼数据
            if (response.data.data.length > 0) {
              this.dataSetFrom.wellbore = this.dataSetFrom.wellboreList[0].id; // 默认选择第一个井眼
              // 获取数据集数据
              if (this.currentNode.dataSetNodeType === 3) {
                return this.getDataSets(this.currentNode.nodeDataId);
              } else {
                return this.getDataSets(this.dataSetFrom.wellbore);
              }
            } else {
              this.$message.warning("该井无井眼数据");
            }
          } else {
            this.$message.error(
              "get wellbore data failed: " + response.data.message
            );
            throw new Error(
              "get wellbore data failed: " + response.data.message
            );
          }
        })
        .catch((error) => {
          console.error("get wellbore data failed:", error);
          throw error;
        })
        .finally(() => {
          this.loading = false; // 关闭加载状态
        });
    },

    getDataSets(wellboreId) {
      return axios
        .get(
          `${this.vWebApiUrl}/oil/logdatafile/GetDatasetListByWellboreId?wellboreId=${wellboreId}`
        )
        .then((response) => {
          if (response.data.success && response.data.data.length > 0) {
            this.dataSetFrom.dataSetList = response.data.data; // 存储数据集数据
            this.dataSetFrom.dataSet = this.dataSetFrom.dataSetList[0].id; // 默认选择第一个数据集
          } else {
            console.log("get dataset data failed");
          }
        })
        .finally(() => {
          this.loading = false; // 关闭加载状态
        });
    },

    getChannels(datasetId, wellboreId, targetNode) {
      return axios
        .get(
          `${this.vWebApiUrl}/oil/logdatafile/GetChannelList?datasetId=${datasetId}&wellboreId=${wellboreId}`
        )
        .then((response) => {
          if (response.data.success && response.data.data.length > 0) {
            this.dataSetFrom.channelList = response.data.data;
            console.log(this.dataSetFrom.channelList);
            // 使用传入的目标节点，而不是 this.currentNode
            targetNode.children = [];
            for (var i = 0; i < this.dataSetFrom.channelList.length; i++) {
              const channelNode = {
                id: this.generateGUID(),
                label: this.dataSetFrom.channelList[i].name,
                children: [],
                moduleType: 4, // 设置 moduleType 属性
                isDataSetNode: 1, // 设置 isDataSetNode 属性 用于判断是数据集节点还是文件节点
                dataSetNodeType: 5, //0-数据文件夹的根目录 1-油田，2-井，3-井眼,4-数据集,5-曲线
                nodeDataId: this.dataSetFrom.channelList[i].id, // 曲线节点的 id
              };

              targetNode.children.push(channelNode);
            }
          } else {
            console.log("get Channels data failed");
          }
        })
        .finally(() => {
          this.loading = false; // 关闭加载状态
        });
    },

    // 找到指定节点的父节点
    findParent(tree, id) {
      for (const node of tree) {
        if (node.children && node.children.length > 0) {
          const index = node.children.findIndex((child) => child.id === id);
          if (index !== -1) {
            return node;
          } else {
            const found = this.findParent(node.children, id);
            if (found) {
              return found;
            }
          }
        }
      }
      return null;
    },
    // 根据节点 id 查找对应节点
    findNodeById(tree, id) {
      for (const node of tree) {
        if (node.id === id) {
          return node; // 找到匹配的节点
        }
        if (node.children && node.children.length > 0) {
          const foundNode = this.findNodeById(node.children, id); // 递归查找
          if (foundNode) {
            return foundNode; // 如果在子节点中找到，返回该节点
          }
        }
      }
      return null; // 如果未找到，返回 null
    },
    //生成节点的唯一标识符
    generateGUID() {
      return "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx".replace(/[x]/g, () => {
        return ((Math.random() * 16) | 0).toString(16);
      });
    },
    // 获取节点及其子节点的 pyModuleId
    getPyModuleIds(node) {
      let pyModuleIds = [];
      if (node.moduleType === 1) {
        // 文件夹节点
        if (node.children && node.children.length > 0) {
          node.children.forEach((child) => {
            pyModuleIds = pyModuleIds.concat(this.getPyModuleIds(child));
          });
        }
      } else if (node.moduleType === 2 && node.pyModuleId) {
        // 文件节点
        pyModuleIds.push(node.pyModuleId);
      }
      return pyModuleIds;
    },
    // 显示树形对话框
    showCodeSampleDialog() {
      this.codeDialogVisible = true;
      axios
        .get(`${this.vWebApiUrl}/sdk/pythonfreemodule/GetCodeSamplesData`)
        .then((response) => {
          if (response.data.success && response.data.data.length > 0) {
            const tree = {};
            const data = response.data.data;

            // 遍历数据，按照 sampleType 分组
            data.forEach((item) => {
              const type = item.sampleType;

              // 如果树中不存在该类型，则创建一个新的父节点
              if (!tree[type]) {
                tree[type] = {
                  id: type, // 使用 sampleType 作为 id
                  moduleType_dialog: 1,
                  label: type, // 使用 sampleType 作为 label
                  children: [], // 初始化子节点数组
                };
              }

              // 创建子节点
              const childNode = {
                id: item.id,
                label: item.sampleName, // sampleName 作为子节点的 label
                sampleCode: item.sampleCode, // 增加 sampleCode 以备后续使用
                createDate: item.createDate,
                lastModified: item.lastModified,
                type: item.sampleType,
                moduleType_dialog: 2,
              };

              // 将当前样本添加到对应的父节点下
              tree[type].children.push(childNode);
            });

            // 将对象转换为数组
            this.treeDataForDialog = Object.values(tree);
            console.log(this.treeDataForDialog); // 输出转换后的树形结构
          }
        })
        .finally(() => {
          this.loading = false; // 关闭加载状态
        });
    },
    // 处理树形对话框中的节点点击事件
    handleTreeNodeClickInDialog(data) {
      axios
        .get(
          `${this.vWebApiUrl}/sdk/pythonfreemodule/GetCodeScrpit?id=${data.id}`
        )
        .then((response) => {
          if (response.data.success) {
            const sampleCode = response.data.data.sampleCode;
            if (data.type == "Basic") {
              const code = `from LoghubApi import *\r\n${sampleCode}`;
              this.selectedNodeText = code;
            } else {
              this.selectedNodeText = sampleCode;
            }
          } else {
            this.$message.error("get code sample failed: ");
          }
        });
    },
    // 处理确认选择
    copyCodeToEditor() {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.selectedNodeText);
      } else {
        this.fallbackCopyText(this.selectedNodeText);
      }
      this.codeDialogVisible = false;
      this.$message({
        message: "Copy successful.",
        type: "success",
      });
    },
    fallbackCopyText(text) {
      const textarea = document.createElement("textarea");
      textarea.value = text;
      textarea.style.position = "fixed"; // Avoid scrolling to bottom
      document.body.appendChild(textarea);
      textarea.select();
      try {
        document.execCommand("copy");
        console.log("Fallback copy successful");
      } catch (err) {
        console.error("Fallback copy failed:", err);
      } finally {
        document.body.removeChild(textarea);
      }
    },
    setCheckNode(id) {
      this.$refs.tree.setCurrentKey(id);
      this.currentNode = this.findNodeById(this.treeData, id);
    },
    //保存文件
    updateTreeData(treeData) {
      const profileData = JSON.parse(treeData);
      this.treeData = profileData; // 更新树形数据
      
      // 计算默认展开的节点，排除 dataSetNodeType=4 的节点
      this.defaultExpandedKeys = this.calculateDefaultExpandedKeys(this.treeData);
    },
    //pythonApp后台服务根据工程树内容初始化
    create_tempfile() {
      this.getCurrentUserId()
        .then((userId) => {
          const hash = window.location.hash;
          const params = new URLSearchParams(hash.split('?')[1]);
          const appId = params.get("appId");

          console.log("----" + this.treeData[0].label);
          const response = axios.post(
            "http://127.0.0.1:5050/create_tempfile",
            {
              user_id: this.userId,
              project_name: this.treeData[0].label,
              code: "",
              appId: appId,
              projectId: this.id,
            },
            {
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
              },
            }
          );
          const data = response.data;
        })
        .catch((error) => {
          console.error("获取用户ID失败:", error);
          // 在这里处理错误
        });
    },
    //MQTT客户端相关方法
    connect() {
            // 使用 WebSocket 连接
            this.client = mqtt.connect('ws://localhost:8083');//这里在C:\Program Files\mosquitto下的mosquitto.conf中配置的
            
            this.client.on('connect', () => {
                console.log('✅ 已连接到MQTT服务器');
                this.subscribe();
            });
            
            this.client.on('error', (error) => {
              console.log(`❌ 连接错误: ${error.message}`);
            });
            //接收到信息后的处理
            this.client.on('message', (topic, message) => {
              console.log(`📥 [${topic}]: ${message.toString()}`);
              
              // 当接收到消息时，重新加载所有 dataSetNodeType = 4 节点下的数据
              this.reloadAllDataSetNodes();
            });
            
            this.client.on('disconnect', () => {
              console.log('⚠️ 连接已断开');
            });
        },        
    disconnect() {
        if (this.client) {
            this.client.end();
            console.log('👋 已断开连接');
        }
    },
    publish() {
        const topic = "";//要发布到的主题
        const message = "";//发布的消息
        
        if (this.client && this.client.connected) {
            this.client.publish(topic, message);
            console.log(`📤 [${topic}]: ${message}`);
        } else {
          console.log('❌ publish,未连接到服务器');
        }
    },        
    subscribe() {
        const topic ="CreateCurve" ;//要订阅的主题 
        
        if (this.client && this.client.connected) {
            this.client.subscribe(topic, (err) => {
                if (!err) {
                  console.log(`📢 已订阅: ${topic}`);
                } else {
                  console.log(`❌ 订阅失败: ${err.message}`);
                }
            });
        } else {
          console.log('❌ subscribe,未连接到服务器');
        }
    },
    // 重新加载所有 dataSetNodeType = 4 节点下的数据
    reloadAllDataSetNodes() {
      console.log("开始重新加载所有数据集节点...");
      
      // 递归遍历树结构，找到所有 dataSetNodeType = 4 的节点
      const findDataSetNodes = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return;
        
        nodes.forEach(node => {
          if (node.isDataSetNode === 1 && node.dataSetNodeType === 4) {
            console.log(`重新加载数据集节点: ${node.label}`);
            // 找到父节点（井眼节点）
            const parentNode = this.findParent(this.treeData, node.id);
            if (parentNode) {
              // 重新加载该数据集节点下的曲线数据
              this.getChannels(node.nodeDataId, parentNode.nodeDataId, node);
            }
          }
          
          // 递归处理子节点
          if (node.children && node.children.length > 0) {
            findDataSetNodes(node.children);
          }
        });
      };
      
      // 开始遍历
      findDataSetNodes(this.treeData);
    },

    testMqtt(){
      this.loading = true;
      
      // 测试数据 - 根据 CurveModule 类的结构
      const testCurveData = {
        datasetId: "61e1ab95782b471c8a6cfcaa93353f00",
        channelName: "测试曲线",
        templateName: "默认模板",
        unit: "Api",
        desc: "这是一个测试曲线",
        indexUnit: "m",
        startIndex: 100,
        endIndex: 103,
        indexSpecing: 1,
        dim: 2,
        sampleElements: 2,
        wellboreId: "494f01b2576f44488e73ab04e31c8cd8"
      };

      axios
        .post(`${this.vWebApiUrl}/sdk/pythonfreemodule/CreateCurve`, testCurveData)
        .then((response) => {
          if (response.data.success) {
            this.$message.success("曲线创建成功！");
            console.log("创建的曲线数据:", response.data.data);
            
            // 如果需要，可以在这里处理返回的曲线数据
            // 比如更新树形结构或显示曲线信息
          } else {
            this.$message.error("曲线创建失败: " + response.data.message);
          }
        })
        .catch((error) => {
          console.error("创建曲线时出错:", error);
          this.$message.error("创建曲线时发生错误: " + error.message);
        })
        .finally(() => {
          this.loading = false;
        });
    }

  },
};
</script>

<style lang="less" scoped>
.icon-button {
  font-size: 28px;
  /* 放大图标 */
  margin: 0 5px;
  /* 添加间距 */
  cursor: pointer;
  /* 改变鼠标指针样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  /* 设置按钮宽度 */
  height: 32px;
  /* 设置按钮高度 */
  color: black;
  /* 设置图标颜色 */
}
.el-tree {
  height: 100%;
  background: var(--dock-pane-cache-bg);
  border-radius: 0 10px 0 0;
}
:deep.el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: #dbecfd;
}
</style>
