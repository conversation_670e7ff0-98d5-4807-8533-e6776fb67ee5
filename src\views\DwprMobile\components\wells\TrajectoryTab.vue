<template>
  <div class="trajectory-tab">
    <div class="view-container">
      <van-empty description="待实现" />
    </div>
    <div class="trajectory-container">
      <div class="trajectory-card">
        <div class="card-item" v-for="item in cardItems" :key="item.title">
          <div class="item-icon">
            <van-icon :name="item.icon" :size="24" />
          </div>
          <div class="item">
            <div class="item-title">{{item.title}}</div>
            <div class="item-value">{{item.value}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { showToast } from 'vant'
import { useShowTabbarStore } from '@/stores/useShowTabbar.ts'
const { setShowTabbar } = useShowTabbarStore()
interface CardItem {
  icon: string
  title: string
  value: string
}
const cardItems = ref<CardItem[]>([
  {
    icon: '/src/icon/dwprMobile/trajectory-depth-icon.svg',
    title: 'Depth',
    value: '3200.0 Ft'
  },
  {
    icon:'/src/icon/dwprMobile/trajectory-incl-icon.svg',
    title: 'Inclination',
    value: '78.5°'
  },
  {
    icon:'/src/icon/dwprMobile/trajectory-color-icon.svg',
    title: 'Coordinate',
    value: 'Active'
  }
])
// 生命周期
onMounted(() => {
  setShowTabbar(false)
})
onUnmounted(() => {
  setShowTabbar(true)
})
</script>

<style scoped lang="less">
.trajectory-tab {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
}
.view-container {
  flex: 1;
}
.trajectory-container {
  padding: 1.5rem 0.8125rem 0.625rem;
}
.trajectory-card {
  background: #FFFFFF;
  border-radius: .375rem;
  border: .0625rem solid #E5E5E5;
  box-shadow: 0rem 0rem .625rem 0rem rgba(0, 0, 0, 0.1);
}
.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 1rem;
  padding-right: 0.6875rem;
}
.card-item {
  display: flex;
  .item {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1.5rem 0.75rem 0.75rem;
    border-bottom: 1px solid #e5e5e5;
    .item-title {
      font-weight: 400;
      font-size: 1.125rem;
      color: #737373;
    }
    .item-value {
      font-weight: 500;
      font-size: 1.125rem;
      color: #3d3d3d;
    }
  }
  .card-item:last-child {
    border-bottom: none;
  }
}
</style>
